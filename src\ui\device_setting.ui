<?xml version="1.0" encoding="UTF-8"?>
<ui version="4.0">
 <class>Form</class>
 <widget class="QWidget" name="Form">
  <property name="geometry">
   <rect>
    <x>0</x>
    <y>0</y>
    <width>1130</width>
    <height>667</height>
   </rect>
  </property>
  <property name="windowTitle">
   <string>Form</string>
  </property>
  <layout class="QGridLayout" name="gridLayout_3">
   <item row="0" column="0">
    <spacer name="horizontalSpacer">
     <property name="orientation">
      <enum>Qt::Horizontal</enum>
     </property>
     <property name="sizeHint" stdset="0">
      <size>
       <width>40</width>
       <height>20</height>
      </size>
     </property>
    </spacer>
   </item>
   <item row="0" column="1">
    <widget class="LargeTitleLabel" name="LargeTitleLabel">
     <property name="font">
      <font>
       <family>华文行楷</family>
       <pointsize>44</pointsize>
       <weight>50</weight>
       <bold>false</bold>
      </font>
     </property>
     <property name="text">
      <string>电机控制器</string>
     </property>
    </widget>
   </item>
   <item row="0" column="2">
    <spacer name="horizontalSpacer_2">
     <property name="orientation">
      <enum>Qt::Horizontal</enum>
     </property>
     <property name="sizeHint" stdset="0">
      <size>
       <width>370</width>
       <height>20</height>
      </size>
     </property>
    </spacer>
   </item>
   <item row="1" column="0">
    <widget class="SubtitleLabel" name="SubtitleLabel">
     <property name="sizePolicy">
      <sizepolicy hsizetype="Preferred" vsizetype="Fixed">
       <horstretch>0</horstretch>
       <verstretch>0</verstretch>
      </sizepolicy>
     </property>
     <property name="minimumSize">
      <size>
       <width>0</width>
       <height>50</height>
      </size>
     </property>
     <property name="maximumSize">
      <size>
       <width>16777215</width>
       <height>50</height>
      </size>
     </property>
     <property name="text">
      <string>PID参数监控</string>
     </property>
     <property name="alignment">
      <set>Qt::AlignCenter</set>
     </property>
    </widget>
   </item>
   <item row="1" column="1">
    <widget class="SubtitleLabel" name="SubtitleLabel_2">
     <property name="sizePolicy">
      <sizepolicy hsizetype="Preferred" vsizetype="Fixed">
       <horstretch>0</horstretch>
       <verstretch>0</verstretch>
      </sizepolicy>
     </property>
     <property name="minimumSize">
      <size>
       <width>0</width>
       <height>50</height>
      </size>
     </property>
     <property name="maximumSize">
      <size>
       <width>16777215</width>
       <height>50</height>
      </size>
     </property>
     <property name="baseSize">
      <size>
       <width>0</width>
       <height>50</height>
      </size>
     </property>
     <property name="text">
      <string>PID参数设置</string>
     </property>
     <property name="alignment">
      <set>Qt::AlignCenter</set>
     </property>
    </widget>
   </item>
   <item row="2" column="0">
    <widget class="CardWidget" name="CardWidget">
     <layout class="QGridLayout" name="gridLayout">
      <item row="1" column="1">
       <widget class="TableWidget" name="table_pid_param">
        <property name="sizePolicy">
         <sizepolicy hsizetype="Expanding" vsizetype="Preferred">
          <horstretch>0</horstretch>
          <verstretch>0</verstretch>
         </sizepolicy>
        </property>
        <property name="font">
         <font>
          <pointsize>12</pointsize>
         </font>
        </property>
        <property name="selectRightClickedRow">
         <bool>true</bool>
        </property>
       </widget>
      </item>
      <item row="2" column="1">
       <widget class="TableWidget" name="table_other_param">
        <property name="font">
         <font>
          <pointsize>12</pointsize>
         </font>
        </property>
        <property name="selectRightClickedRow">
         <bool>true</bool>
        </property>
       </widget>
      </item>
     </layout>
    </widget>
   </item>
   <item row="2" column="1" colspan="2">
    <widget class="CardWidget" name="CardWidget_2">
     <layout class="QGridLayout" name="gridLayout_2">
      <item row="4" column="0">
       <widget class="SubtitleLabel" name="label_speed_7">
        <property name="sizePolicy">
         <sizepolicy hsizetype="Preferred" vsizetype="Fixed">
          <horstretch>0</horstretch>
          <verstretch>0</verstretch>
         </sizepolicy>
        </property>
        <property name="minimumSize">
         <size>
          <width>0</width>
          <height>0</height>
         </size>
        </property>
        <property name="text">
         <string>母线电压参考：</string>
        </property>
        <property name="textFormat">
         <enum>Qt::MarkdownText</enum>
        </property>
       </widget>
      </item>
      <item row="4" column="3">
       <widget class="DoubleSpinBox" name="spinbox_vbus_ref1">
        <property name="minimumSize">
         <size>
          <width>0</width>
          <height>44</height>
         </size>
        </property>
        <property name="maximumSize">
         <size>
          <width>16777215</width>
          <height>16777215</height>
         </size>
        </property>
        <property name="decimals">
         <number>6</number>
        </property>
        <property name="maximum">
         <double>50.000000000000000</double>
        </property>
        <property name="singleStep">
         <double>0.000100000000000</double>
        </property>
       </widget>
      </item>
      <item row="9" column="0">
       <widget class="SubtitleLabel" name="label_speed_9">
        <property name="sizePolicy">
         <sizepolicy hsizetype="Preferred" vsizetype="Fixed">
          <horstretch>0</horstretch>
          <verstretch>0</verstretch>
         </sizepolicy>
        </property>
        <property name="minimumSize">
         <size>
          <width>0</width>
          <height>0</height>
         </size>
        </property>
        <property name="text">
         <string>在线升级模式：</string>
        </property>
        <property name="textFormat">
         <enum>Qt::MarkdownText</enum>
        </property>
       </widget>
      </item>
      <item row="10" column="1" colspan="2">
       <widget class="SubtitleLabel" name="label_speed_13">
        <property name="sizePolicy">
         <sizepolicy hsizetype="Preferred" vsizetype="Fixed">
          <horstretch>0</horstretch>
          <verstretch>0</verstretch>
         </sizepolicy>
        </property>
        <property name="minimumSize">
         <size>
          <width>0</width>
          <height>0</height>
         </size>
        </property>
        <property name="text">
         <string>当前未启动旋变校准</string>
        </property>
        <property name="textFormat">
         <enum>Qt::MarkdownText</enum>
        </property>
       </widget>
      </item>
      <item row="8" column="0">
       <widget class="SubtitleLabel" name="label_speed_8">
        <property name="sizePolicy">
         <sizepolicy hsizetype="Preferred" vsizetype="Fixed">
          <horstretch>0</horstretch>
          <verstretch>0</verstretch>
         </sizepolicy>
        </property>
        <property name="minimumSize">
         <size>
          <width>0</width>
          <height>0</height>
         </size>
        </property>
        <property name="text">
         <string>控制模式选择：</string>
        </property>
        <property name="textFormat">
         <enum>Qt::MarkdownText</enum>
        </property>
       </widget>
      </item>
      <item row="2" column="0">
       <widget class="SubtitleLabel" name="label_speed_5">
        <property name="sizePolicy">
         <sizepolicy hsizetype="Preferred" vsizetype="Fixed">
          <horstretch>0</horstretch>
          <verstretch>0</verstretch>
         </sizepolicy>
        </property>
        <property name="minimumSize">
         <size>
          <width>0</width>
          <height>0</height>
         </size>
        </property>
        <property name="text">
         <string>电流环D：</string>
        </property>
        <property name="textFormat">
         <enum>Qt::MarkdownText</enum>
        </property>
       </widget>
      </item>
      <item row="10" column="3">
       <widget class="PillPushButton" name="btn_start_resolver_calibration">
        <property name="minimumSize">
         <size>
          <width>0</width>
          <height>44</height>
         </size>
        </property>
        <property name="text">
         <string>启动旋变校准</string>
        </property>
        <property name="checkable">
         <bool>false</bool>
        </property>
       </widget>
      </item>
      <item row="4" column="2">
       <widget class="ToolButton" name="btn_vbus_ref">
        <property name="minimumSize">
         <size>
          <width>44</width>
          <height>44</height>
         </size>
        </property>
        <property name="icon">
         <iconset>
          <normaloff>../../img/save2.svg</normaloff>../../img/save2.svg</iconset>
        </property>
        <property name="iconSize">
         <size>
          <width>22</width>
          <height>22</height>
         </size>
        </property>
       </widget>
      </item>
      <item row="3" column="2">
       <widget class="ToolButton" name="btn_iq_pid_p">
        <property name="minimumSize">
         <size>
          <width>44</width>
          <height>44</height>
         </size>
        </property>
        <property name="icon">
         <iconset>
          <normaloff>../../img/save2.svg</normaloff>../../img/save2.svg</iconset>
        </property>
        <property name="iconSize">
         <size>
          <width>22</width>
          <height>22</height>
         </size>
        </property>
       </widget>
      </item>
      <item row="1" column="0">
       <widget class="SubtitleLabel" name="label_speed">
        <property name="sizePolicy">
         <sizepolicy hsizetype="Preferred" vsizetype="Fixed">
          <horstretch>0</horstretch>
          <verstretch>0</verstretch>
         </sizepolicy>
        </property>
        <property name="minimumSize">
         <size>
          <width>0</width>
          <height>0</height>
         </size>
        </property>
        <property name="text">
         <string>速度环：</string>
        </property>
        <property name="textFormat">
         <enum>Qt::MarkdownText</enum>
        </property>
       </widget>
      </item>
      <item row="2" column="2">
       <widget class="ToolButton" name="btn_id_pid_p">
        <property name="minimumSize">
         <size>
          <width>44</width>
          <height>44</height>
         </size>
        </property>
        <property name="icon">
         <iconset>
          <normaloff>../../img/save2.svg</normaloff>../../img/save2.svg</iconset>
        </property>
        <property name="iconSize">
         <size>
          <width>22</width>
          <height>22</height>
         </size>
        </property>
       </widget>
      </item>
      <item row="1" column="3">
       <widget class="DoubleSpinBox" name="spinbox_speed_pid_i">
        <property name="minimumSize">
         <size>
          <width>0</width>
          <height>44</height>
         </size>
        </property>
        <property name="maximumSize">
         <size>
          <width>16777215</width>
          <height>16777215</height>
         </size>
        </property>
        <property name="decimals">
         <number>6</number>
        </property>
        <property name="maximum">
         <double>50.000000000000000</double>
        </property>
        <property name="singleStep">
         <double>0.000100000000000</double>
        </property>
       </widget>
      </item>
      <item row="1" column="1">
       <widget class="DoubleSpinBox" name="spinbox_speed_pid_p">
        <property name="minimumSize">
         <size>
          <width>0</width>
          <height>44</height>
         </size>
        </property>
        <property name="maximumSize">
         <size>
          <width>16777215</width>
          <height>16777215</height>
         </size>
        </property>
        <property name="decimals">
         <number>6</number>
        </property>
        <property name="maximum">
         <double>50.000000000000000</double>
        </property>
        <property name="singleStep">
         <double>0.000100000000000</double>
        </property>
       </widget>
      </item>
      <item row="2" column="3">
       <widget class="DoubleSpinBox" name="spinbox_id_pid_i">
        <property name="minimumSize">
         <size>
          <width>0</width>
          <height>44</height>
         </size>
        </property>
        <property name="maximumSize">
         <size>
          <width>16777215</width>
          <height>16777215</height>
         </size>
        </property>
        <property name="decimals">
         <number>6</number>
        </property>
        <property name="maximum">
         <double>50.000000000000000</double>
        </property>
        <property name="singleStep">
         <double>0.000100000000000</double>
        </property>
       </widget>
      </item>
      <item row="6" column="0">
       <widget class="SubtitleLabel" name="label_speed_17">
        <property name="sizePolicy">
         <sizepolicy hsizetype="Preferred" vsizetype="Fixed">
          <horstretch>0</horstretch>
          <verstretch>0</verstretch>
         </sizepolicy>
        </property>
        <property name="minimumSize">
         <size>
          <width>0</width>
          <height>0</height>
         </size>
        </property>
        <property name="text">
         <string>Motor_Resolver_Zero：</string>
        </property>
        <property name="textFormat">
         <enum>Qt::MarkdownText</enum>
        </property>
       </widget>
      </item>
      <item row="4" column="4">
       <widget class="ToolButton" name="btn_vbus_ref1">
        <property name="minimumSize">
         <size>
          <width>44</width>
          <height>44</height>
         </size>
        </property>
        <property name="icon">
         <iconset>
          <normaloff>../../img/save2.svg</normaloff>../../img/save2.svg</iconset>
        </property>
        <property name="iconSize">
         <size>
          <width>22</width>
          <height>22</height>
         </size>
        </property>
       </widget>
      </item>
      <item row="9" column="1" colspan="2">
       <widget class="SubtitleLabel" name="label_speed_11">
        <property name="sizePolicy">
         <sizepolicy hsizetype="Preferred" vsizetype="Fixed">
          <horstretch>0</horstretch>
          <verstretch>0</verstretch>
         </sizepolicy>
        </property>
        <property name="minimumSize">
         <size>
          <width>0</width>
          <height>0</height>
         </size>
        </property>
        <property name="text">
         <string>当前未启动升级模式</string>
        </property>
        <property name="textFormat">
         <enum>Qt::MarkdownText</enum>
        </property>
       </widget>
      </item>
      <item row="6" column="1">
       <widget class="DoubleSpinBox" name="spinbox_Motor_Resolver_Zero">
        <property name="minimumSize">
         <size>
          <width>0</width>
          <height>44</height>
         </size>
        </property>
        <property name="maximumSize">
         <size>
          <width>16777215</width>
          <height>16777215</height>
         </size>
        </property>
        <property name="decimals">
         <number>6</number>
        </property>
        <property name="maximum">
         <double>50.000000000000000</double>
        </property>
        <property name="singleStep">
         <double>0.000100000000000</double>
        </property>
       </widget>
      </item>
      <item row="3" column="0">
       <widget class="SubtitleLabel" name="label_speed_6">
        <property name="sizePolicy">
         <sizepolicy hsizetype="Preferred" vsizetype="Fixed">
          <horstretch>0</horstretch>
          <verstretch>0</verstretch>
         </sizepolicy>
        </property>
        <property name="minimumSize">
         <size>
          <width>0</width>
          <height>0</height>
         </size>
        </property>
        <property name="text">
         <string>电流环Q：     </string>
        </property>
        <property name="textFormat">
         <enum>Qt::MarkdownText</enum>
        </property>
       </widget>
      </item>
      <item row="3" column="1">
       <widget class="DoubleSpinBox" name="spinbox_iq_pid_p">
        <property name="minimumSize">
         <size>
          <width>0</width>
          <height>44</height>
         </size>
        </property>
        <property name="maximumSize">
         <size>
          <width>16777215</width>
          <height>16777215</height>
         </size>
        </property>
        <property name="decimals">
         <number>6</number>
        </property>
        <property name="maximum">
         <double>50.000000000000000</double>
        </property>
        <property name="singleStep">
         <double>0.000100000000000</double>
        </property>
       </widget>
      </item>
      <item row="11" column="1" colspan="2">
       <widget class="SubtitleLabel" name="label_speed_15">
        <property name="sizePolicy">
         <sizepolicy hsizetype="Preferred" vsizetype="Fixed">
          <horstretch>0</horstretch>
          <verstretch>0</verstretch>
         </sizepolicy>
        </property>
        <property name="minimumSize">
         <size>
          <width>0</width>
          <height>0</height>
         </size>
        </property>
        <property name="text">
         <string/>
        </property>
        <property name="textFormat">
         <enum>Qt::MarkdownText</enum>
        </property>
       </widget>
      </item>
      <item row="0" column="3">
       <widget class="SubtitleLabel" name="label_speed_3">
        <property name="sizePolicy">
         <sizepolicy hsizetype="Preferred" vsizetype="Fixed">
          <horstretch>0</horstretch>
          <verstretch>0</verstretch>
         </sizepolicy>
        </property>
        <property name="minimumSize">
         <size>
          <width>0</width>
          <height>0</height>
         </size>
        </property>
        <property name="text">
         <string>I</string>
        </property>
        <property name="textFormat">
         <enum>Qt::MarkdownText</enum>
        </property>
        <property name="alignment">
         <set>Qt::AlignCenter</set>
        </property>
       </widget>
      </item>
      <item row="8" column="1" colspan="2">
       <widget class="SubtitleLabel" name="label_speed_10">
        <property name="sizePolicy">
         <sizepolicy hsizetype="Preferred" vsizetype="Fixed">
          <horstretch>0</horstretch>
          <verstretch>0</verstretch>
         </sizepolicy>
        </property>
        <property name="minimumSize">
         <size>
          <width>0</width>
          <height>0</height>
         </size>
        </property>
        <property name="text">
         <string>当前为速度环模式</string>
        </property>
        <property name="textFormat">
         <enum>Qt::MarkdownText</enum>
        </property>
       </widget>
      </item>
      <item row="6" column="2">
       <widget class="ToolButton" name="btn_Motor_Resolver_Zero">
        <property name="minimumSize">
         <size>
          <width>44</width>
          <height>44</height>
         </size>
        </property>
        <property name="icon">
         <iconset>
          <normaloff>../../img/save2.svg</normaloff>../../img/save2.svg</iconset>
        </property>
        <property name="iconSize">
         <size>
          <width>22</width>
          <height>22</height>
         </size>
        </property>
       </widget>
      </item>
      <item row="1" column="4">
       <widget class="ToolButton" name="btn_speed_pid_i">
        <property name="minimumSize">
         <size>
          <width>44</width>
          <height>44</height>
         </size>
        </property>
        <property name="icon">
         <iconset>
          <normaloff>../../img/save2.svg</normaloff>../../img/save2.svg</iconset>
        </property>
        <property name="iconSize">
         <size>
          <width>22</width>
          <height>22</height>
         </size>
        </property>
       </widget>
      </item>
      <item row="3" column="4">
       <widget class="ToolButton" name="btn_iq_pid_i">
        <property name="minimumSize">
         <size>
          <width>44</width>
          <height>44</height>
         </size>
        </property>
        <property name="icon">
         <iconset>
          <normaloff>../../img/save2.svg</normaloff>../../img/save2.svg</iconset>
        </property>
        <property name="iconSize">
         <size>
          <width>22</width>
          <height>22</height>
         </size>
        </property>
       </widget>
      </item>
      <item row="9" column="3">
       <widget class="SwitchButton" name="sw_btn_upgrade_mode_select">
        <property name="text">
         <string>正常模式</string>
        </property>
        <property name="onText">
         <string>升级模式</string>
        </property>
        <property name="offText">
         <string>正常模式</string>
        </property>
       </widget>
      </item>
      <item row="11" column="0">
       <widget class="SubtitleLabel" name="label_speed_14">
        <property name="sizePolicy">
         <sizepolicy hsizetype="Preferred" vsizetype="Fixed">
          <horstretch>0</horstretch>
          <verstretch>0</verstretch>
         </sizepolicy>
        </property>
        <property name="minimumSize">
         <size>
          <width>0</width>
          <height>0</height>
         </size>
        </property>
        <property name="text">
         <string>永久保存参数：</string>
        </property>
        <property name="textFormat">
         <enum>Qt::MarkdownText</enum>
        </property>
       </widget>
      </item>
      <item row="4" column="1">
       <widget class="DoubleSpinBox" name="spinbox_vbus_ref">
        <property name="minimumSize">
         <size>
          <width>0</width>
          <height>44</height>
         </size>
        </property>
        <property name="maximumSize">
         <size>
          <width>16777215</width>
          <height>16777215</height>
         </size>
        </property>
        <property name="decimals">
         <number>6</number>
        </property>
        <property name="maximum">
         <double>50.000000000000000</double>
        </property>
        <property name="singleStep">
         <double>0.000100000000000</double>
        </property>
       </widget>
      </item>
      <item row="2" column="4">
       <widget class="ToolButton" name="btn_id_pid_i">
        <property name="minimumSize">
         <size>
          <width>44</width>
          <height>44</height>
         </size>
        </property>
        <property name="icon">
         <iconset>
          <normaloff>../../img/save2.svg</normaloff>../../img/save2.svg</iconset>
        </property>
        <property name="iconSize">
         <size>
          <width>22</width>
          <height>22</height>
         </size>
        </property>
       </widget>
      </item>
      <item row="5" column="2">
       <widget class="ToolButton" name="btn_Rottx_Zero_Current">
        <property name="minimumSize">
         <size>
          <width>44</width>
          <height>44</height>
         </size>
        </property>
        <property name="icon">
         <iconset>
          <normaloff>../../img/save2.svg</normaloff>../../img/save2.svg</iconset>
        </property>
        <property name="iconSize">
         <size>
          <width>22</width>
          <height>22</height>
         </size>
        </property>
       </widget>
      </item>
      <item row="2" column="1">
       <widget class="DoubleSpinBox" name="spinbox_id_pid_p">
        <property name="minimumSize">
         <size>
          <width>0</width>
          <height>44</height>
         </size>
        </property>
        <property name="maximumSize">
         <size>
          <width>16777215</width>
          <height>16777215</height>
         </size>
        </property>
        <property name="decimals">
         <number>6</number>
        </property>
        <property name="maximum">
         <double>50.000000000000000</double>
        </property>
        <property name="singleStep">
         <double>0.000100000000000</double>
        </property>
       </widget>
      </item>
      <item row="1" column="2">
       <widget class="ToolButton" name="btn_speed_pid_p">
        <property name="minimumSize">
         <size>
          <width>44</width>
          <height>44</height>
         </size>
        </property>
        <property name="icon">
         <iconset>
          <normaloff>../../img/save2.svg</normaloff>../../img/save2.svg</iconset>
        </property>
        <property name="iconSize">
         <size>
          <width>22</width>
          <height>22</height>
         </size>
        </property>
       </widget>
      </item>
      <item row="5" column="0">
       <widget class="SubtitleLabel" name="label_speed_16">
        <property name="sizePolicy">
         <sizepolicy hsizetype="Preferred" vsizetype="Fixed">
          <horstretch>0</horstretch>
          <verstretch>0</verstretch>
         </sizepolicy>
        </property>
        <property name="minimumSize">
         <size>
          <width>0</width>
          <height>0</height>
         </size>
        </property>
        <property name="text">
         <string>Rottx_Zero_Current：</string>
        </property>
        <property name="textFormat">
         <enum>Qt::MarkdownText</enum>
        </property>
       </widget>
      </item>
      <item row="11" column="3">
       <widget class="PillPushButton" name="btn_save_param">
        <property name="minimumSize">
         <size>
          <width>0</width>
          <height>44</height>
         </size>
        </property>
        <property name="text">
         <string>保存参数命令</string>
        </property>
        <property name="checkable">
         <bool>false</bool>
        </property>
       </widget>
      </item>
      <item row="5" column="1">
       <widget class="DoubleSpinBox" name="spinbox_Rottx_Zero_Current">
        <property name="minimumSize">
         <size>
          <width>0</width>
          <height>44</height>
         </size>
        </property>
        <property name="maximumSize">
         <size>
          <width>16777215</width>
          <height>16777215</height>
         </size>
        </property>
        <property name="decimals">
         <number>6</number>
        </property>
        <property name="maximum">
         <double>50.000000000000000</double>
        </property>
        <property name="singleStep">
         <double>0.000100000000000</double>
        </property>
       </widget>
      </item>
      <item row="8" column="3">
       <widget class="SwitchButton" name="sw_btn_ctrl_mode_select">
        <property name="text">
         <string>速度环模式</string>
        </property>
        <property name="onText">
         <string>电流环模式</string>
        </property>
        <property name="offText">
         <string>速度环模式</string>
        </property>
       </widget>
      </item>
      <item row="10" column="0">
       <widget class="SubtitleLabel" name="label_speed_12">
        <property name="sizePolicy">
         <sizepolicy hsizetype="Preferred" vsizetype="Fixed">
          <horstretch>0</horstretch>
          <verstretch>0</verstretch>
         </sizepolicy>
        </property>
        <property name="minimumSize">
         <size>
          <width>0</width>
          <height>0</height>
         </size>
        </property>
        <property name="text">
         <string>旋变零点校准：</string>
        </property>
        <property name="textFormat">
         <enum>Qt::MarkdownText</enum>
        </property>
       </widget>
      </item>
      <item row="0" column="1">
       <widget class="SubtitleLabel" name="label_speed_2">
        <property name="sizePolicy">
         <sizepolicy hsizetype="Preferred" vsizetype="Fixed">
          <horstretch>0</horstretch>
          <verstretch>0</verstretch>
         </sizepolicy>
        </property>
        <property name="minimumSize">
         <size>
          <width>0</width>
          <height>0</height>
         </size>
        </property>
        <property name="text">
         <string>P</string>
        </property>
        <property name="textFormat">
         <enum>Qt::MarkdownText</enum>
        </property>
        <property name="alignment">
         <set>Qt::AlignCenter</set>
        </property>
       </widget>
      </item>
      <item row="3" column="3">
       <widget class="DoubleSpinBox" name="spinbox_iq_pid_i">
        <property name="minimumSize">
         <size>
          <width>0</width>
          <height>44</height>
         </size>
        </property>
        <property name="maximumSize">
         <size>
          <width>16777215</width>
          <height>16777215</height>
         </size>
        </property>
        <property name="decimals">
         <number>6</number>
        </property>
        <property name="maximum">
         <double>50.000000000000000</double>
        </property>
        <property name="singleStep">
         <double>0.000100000000000</double>
        </property>
       </widget>
      </item>
      <item row="7" column="0">
       <widget class="SubtitleLabel" name="label_speed_18">
        <property name="sizePolicy">
         <sizepolicy hsizetype="Preferred" vsizetype="Fixed">
          <horstretch>0</horstretch>
          <verstretch>0</verstretch>
         </sizepolicy>
        </property>
        <property name="minimumSize">
         <size>
          <width>0</width>
          <height>0</height>
         </size>
        </property>
        <property name="text">
         <string>Motor_Pn：</string>
        </property>
        <property name="textFormat">
         <enum>Qt::MarkdownText</enum>
        </property>
       </widget>
      </item>
      <item row="7" column="1">
       <widget class="DoubleSpinBox" name="spinbox_Motor_Pn">
        <property name="minimumSize">
         <size>
          <width>0</width>
          <height>44</height>
         </size>
        </property>
        <property name="maximumSize">
         <size>
          <width>16777215</width>
          <height>16777215</height>
         </size>
        </property>
        <property name="decimals">
         <number>6</number>
        </property>
        <property name="maximum">
         <double>50.000000000000000</double>
        </property>
        <property name="singleStep">
         <double>0.000100000000000</double>
        </property>
       </widget>
      </item>
      <item row="7" column="2">
       <widget class="ToolButton" name="btn_Motor_Pn">
        <property name="minimumSize">
         <size>
          <width>44</width>
          <height>44</height>
         </size>
        </property>
        <property name="icon">
         <iconset>
          <normaloff>../../img/save2.svg</normaloff>../../img/save2.svg</iconset>
        </property>
        <property name="iconSize">
         <size>
          <width>22</width>
          <height>22</height>
         </size>
        </property>
       </widget>
      </item>
     </layout>
    </widget>
   </item>
  </layout>
 </widget>
 <customwidgets>
  <customwidget>
   <class>PushButton</class>
   <extends>QPushButton</extends>
   <header>qfluentwidgets</header>
  </customwidget>
  <customwidget>
   <class>PillPushButton</class>
   <extends>ToggleButton</extends>
   <header>qfluentwidgets</header>
  </customwidget>
  <customwidget>
   <class>ToolButton</class>
   <extends>QToolButton</extends>
   <header>qfluentwidgets</header>
  </customwidget>
  <customwidget>
   <class>SwitchButton</class>
   <extends>QWidget</extends>
   <header>qfluentwidgets</header>
  </customwidget>
  <customwidget>
   <class>ToggleButton</class>
   <extends>PushButton</extends>
   <header>qfluentwidgets</header>
  </customwidget>
  <customwidget>
   <class>CardWidget</class>
   <extends>QFrame</extends>
   <header>qfluentwidgets</header>
   <container>1</container>
  </customwidget>
  <customwidget>
   <class>SubtitleLabel</class>
   <extends>QLabel</extends>
   <header>qfluentwidgets</header>
  </customwidget>
  <customwidget>
   <class>LargeTitleLabel</class>
   <extends>QLabel</extends>
   <header>qfluentwidgets</header>
  </customwidget>
  <customwidget>
   <class>DoubleSpinBox</class>
   <extends>QDoubleSpinBox</extends>
   <header>qfluentwidgets</header>
  </customwidget>
  <customwidget>
   <class>TableWidget</class>
   <extends>QTableWidget</extends>
   <header>qfluentwidgets</header>
  </customwidget>
 </customwidgets>
 <resources/>
 <connections/>
</ui>
