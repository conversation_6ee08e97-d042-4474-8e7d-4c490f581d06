<?xml version="1.0" encoding="UTF-8"?>
<ui version="4.0">
 <class>Form</class>
 <widget class="QWidget" name="Form">
  <property name="geometry">
   <rect>
    <x>0</x>
    <y>0</y>
    <width>1008</width>
    <height>731</height>
   </rect>
  </property>
  <property name="windowTitle">
   <string>Form</string>
  </property>
  <layout class="QGridLayout" name="gridLayout_3">
   <item row="1" column="0">
    <widget class="CardWidget" name="CardWidget">
     <property name="sizePolicy">
      <sizepolicy hsizetype="Preferred" vsizetype="Expanding">
       <horstretch>0</horstretch>
       <verstretch>0</verstretch>
      </sizepolicy>
     </property>
     <layout class="QGridLayout" name="gridLayout">
      <item row="0" column="0">
       <widget class="SubtitleLabel" name="label_vbus_in_2">
        <property name="sizePolicy">
         <sizepolicy hsizetype="Fixed" vsizetype="Fixed">
          <horstretch>0</horstretch>
          <verstretch>0</verstretch>
         </sizepolicy>
        </property>
        <property name="minimumSize">
         <size>
          <width>80</width>
          <height>0</height>
         </size>
        </property>
        <property name="text">
         <string>Vbus_in:</string>
        </property>
        <property name="textFormat">
         <enum>Qt::MarkdownText</enum>
        </property>
        <property name="lightColor" stdset="0">
         <color>
          <red>255</red>
          <green>5</green>
          <blue>30</blue>
         </color>
        </property>
       </widget>
      </item>
      <item row="0" column="1">
       <widget class="SubtitleLabel" name="label_vbus_in">
        <property name="sizePolicy">
         <sizepolicy hsizetype="Preferred" vsizetype="Fixed">
          <horstretch>0</horstretch>
          <verstretch>0</verstretch>
         </sizepolicy>
        </property>
        <property name="text">
         <string/>
        </property>
        <property name="textFormat">
         <enum>Qt::MarkdownText</enum>
        </property>
        <property name="lightColor" stdset="0">
         <color>
          <red>255</red>
          <green>5</green>
          <blue>30</blue>
         </color>
        </property>
       </widget>
      </item>
      <item row="0" column="2">
       <widget class="SubtitleLabel" name="label_vdc_2">
        <property name="sizePolicy">
         <sizepolicy hsizetype="Fixed" vsizetype="Fixed">
          <horstretch>0</horstretch>
          <verstretch>0</verstretch>
         </sizepolicy>
        </property>
        <property name="minimumSize">
         <size>
          <width>50</width>
          <height>0</height>
         </size>
        </property>
        <property name="text">
         <string>Vdc:</string>
        </property>
        <property name="textFormat">
         <enum>Qt::MarkdownText</enum>
        </property>
        <property name="lightColor" stdset="0">
         <color>
          <red>255</red>
          <green>5</green>
          <blue>30</blue>
         </color>
        </property>
        <property name="lightCustomQss" stdset="0">
         <string>FluentLabelBase{color:#000000}</string>
        </property>
       </widget>
      </item>
      <item row="0" column="3">
       <widget class="SubtitleLabel" name="label_vdc">
        <property name="sizePolicy">
         <sizepolicy hsizetype="Preferred" vsizetype="Fixed">
          <horstretch>0</horstretch>
          <verstretch>0</verstretch>
         </sizepolicy>
        </property>
        <property name="text">
         <string/>
        </property>
        <property name="textFormat">
         <enum>Qt::MarkdownText</enum>
        </property>
        <property name="lightColor" stdset="0">
         <color>
          <red>255</red>
          <green>5</green>
          <blue>30</blue>
         </color>
        </property>
        <property name="lightCustomQss" stdset="0">
         <string>FluentLabelBase{color:#000000}</string>
        </property>
       </widget>
      </item>
      <item row="1" column="0">
       <widget class="SubtitleLabel" name="label_ud_2">
        <property name="sizePolicy">
         <sizepolicy hsizetype="Fixed" vsizetype="Fixed">
          <horstretch>0</horstretch>
          <verstretch>0</verstretch>
         </sizepolicy>
        </property>
        <property name="minimumSize">
         <size>
          <width>80</width>
          <height>0</height>
         </size>
        </property>
        <property name="text">
         <string>Ud:</string>
        </property>
        <property name="textFormat">
         <enum>Qt::MarkdownText</enum>
        </property>
        <property name="lightColor" stdset="0">
         <color>
          <red>213</red>
          <green>110</green>
          <blue>7</blue>
         </color>
        </property>
       </widget>
      </item>
      <item row="1" column="1">
       <widget class="SubtitleLabel" name="label_ud">
        <property name="sizePolicy">
         <sizepolicy hsizetype="Preferred" vsizetype="Fixed">
          <horstretch>0</horstretch>
          <verstretch>0</verstretch>
         </sizepolicy>
        </property>
        <property name="minimumSize">
         <size>
          <width>0</width>
          <height>0</height>
         </size>
        </property>
        <property name="text">
         <string/>
        </property>
        <property name="textFormat">
         <enum>Qt::MarkdownText</enum>
        </property>
        <property name="lightColor" stdset="0">
         <color>
          <red>213</red>
          <green>110</green>
          <blue>7</blue>
         </color>
        </property>
       </widget>
      </item>
      <item row="1" column="2">
       <widget class="SubtitleLabel" name="label_ibus_2">
        <property name="sizePolicy">
         <sizepolicy hsizetype="Fixed" vsizetype="Fixed">
          <horstretch>0</horstretch>
          <verstretch>0</verstretch>
         </sizepolicy>
        </property>
        <property name="minimumSize">
         <size>
          <width>50</width>
          <height>0</height>
         </size>
        </property>
        <property name="text">
         <string>Ibus:</string>
        </property>
        <property name="textFormat">
         <enum>Qt::MarkdownText</enum>
        </property>
        <property name="lightColor" stdset="0">
         <color>
          <red>255</red>
          <green>5</green>
          <blue>30</blue>
         </color>
        </property>
       </widget>
      </item>
      <item row="1" column="3">
       <widget class="SubtitleLabel" name="label_ibus">
        <property name="sizePolicy">
         <sizepolicy hsizetype="Preferred" vsizetype="Fixed">
          <horstretch>0</horstretch>
          <verstretch>0</verstretch>
         </sizepolicy>
        </property>
        <property name="text">
         <string/>
        </property>
        <property name="textFormat">
         <enum>Qt::MarkdownText</enum>
        </property>
        <property name="lightColor" stdset="0">
         <color>
          <red>255</red>
          <green>5</green>
          <blue>30</blue>
         </color>
        </property>
       </widget>
      </item>
      <item row="2" column="0">
       <widget class="SubtitleLabel" name="label_uq_2">
        <property name="sizePolicy">
         <sizepolicy hsizetype="Fixed" vsizetype="Fixed">
          <horstretch>0</horstretch>
          <verstretch>0</verstretch>
         </sizepolicy>
        </property>
        <property name="minimumSize">
         <size>
          <width>80</width>
          <height>0</height>
         </size>
        </property>
        <property name="text">
         <string>Uq:</string>
        </property>
        <property name="textFormat">
         <enum>Qt::MarkdownText</enum>
        </property>
        <property name="lightColor" stdset="0">
         <color>
          <red>213</red>
          <green>0</green>
          <blue>39</blue>
         </color>
        </property>
       </widget>
      </item>
      <item row="2" column="1">
       <widget class="SubtitleLabel" name="label_uq">
        <property name="sizePolicy">
         <sizepolicy hsizetype="Preferred" vsizetype="Fixed">
          <horstretch>0</horstretch>
          <verstretch>0</verstretch>
         </sizepolicy>
        </property>
        <property name="text">
         <string/>
        </property>
        <property name="textFormat">
         <enum>Qt::MarkdownText</enum>
        </property>
        <property name="lightColor" stdset="0">
         <color>
          <red>213</red>
          <green>0</green>
          <blue>39</blue>
         </color>
        </property>
       </widget>
      </item>
      <item row="2" column="2">
       <widget class="SubtitleLabel" name="label_ia_2">
        <property name="sizePolicy">
         <sizepolicy hsizetype="Fixed" vsizetype="Fixed">
          <horstretch>0</horstretch>
          <verstretch>0</verstretch>
         </sizepolicy>
        </property>
        <property name="minimumSize">
         <size>
          <width>50</width>
          <height>0</height>
         </size>
        </property>
        <property name="text">
         <string>Ia:</string>
        </property>
        <property name="textFormat">
         <enum>Qt::MarkdownText</enum>
        </property>
        <property name="lightColor" stdset="0">
         <color>
          <red>225</red>
          <green>229</green>
          <blue>0</blue>
         </color>
        </property>
       </widget>
      </item>
      <item row="2" column="3">
       <widget class="SubtitleLabel" name="label_ia">
        <property name="sizePolicy">
         <sizepolicy hsizetype="Preferred" vsizetype="Fixed">
          <horstretch>0</horstretch>
          <verstretch>0</verstretch>
         </sizepolicy>
        </property>
        <property name="text">
         <string/>
        </property>
        <property name="textFormat">
         <enum>Qt::MarkdownText</enum>
        </property>
        <property name="lightColor" stdset="0">
         <color>
          <red>225</red>
          <green>229</green>
          <blue>0</blue>
         </color>
        </property>
       </widget>
      </item>
      <item row="3" column="0">
       <widget class="SubtitleLabel" name="label_id_2">
        <property name="sizePolicy">
         <sizepolicy hsizetype="Fixed" vsizetype="Fixed">
          <horstretch>0</horstretch>
          <verstretch>0</verstretch>
         </sizepolicy>
        </property>
        <property name="minimumSize">
         <size>
          <width>80</width>
          <height>0</height>
         </size>
        </property>
        <property name="text">
         <string>Id:</string>
        </property>
        <property name="textFormat">
         <enum>Qt::MarkdownText</enum>
        </property>
        <property name="lightColor" stdset="0">
         <color>
          <red>213</red>
          <green>110</green>
          <blue>7</blue>
         </color>
        </property>
       </widget>
      </item>
      <item row="3" column="1">
       <widget class="SubtitleLabel" name="label_id">
        <property name="sizePolicy">
         <sizepolicy hsizetype="Preferred" vsizetype="Fixed">
          <horstretch>0</horstretch>
          <verstretch>0</verstretch>
         </sizepolicy>
        </property>
        <property name="text">
         <string/>
        </property>
        <property name="textFormat">
         <enum>Qt::MarkdownText</enum>
        </property>
        <property name="lightColor" stdset="0">
         <color>
          <red>213</red>
          <green>110</green>
          <blue>7</blue>
         </color>
        </property>
       </widget>
      </item>
      <item row="3" column="2">
       <widget class="SubtitleLabel" name="label_ib_2">
        <property name="sizePolicy">
         <sizepolicy hsizetype="Fixed" vsizetype="Fixed">
          <horstretch>0</horstretch>
          <verstretch>0</verstretch>
         </sizepolicy>
        </property>
        <property name="minimumSize">
         <size>
          <width>50</width>
          <height>0</height>
         </size>
        </property>
        <property name="text">
         <string>Ib:</string>
        </property>
        <property name="textFormat">
         <enum>Qt::MarkdownText</enum>
        </property>
        <property name="lightColor" stdset="0">
         <color>
          <red>78</red>
          <green>200</green>
          <blue>12</blue>
         </color>
        </property>
       </widget>
      </item>
      <item row="3" column="3">
       <widget class="SubtitleLabel" name="label_ib">
        <property name="sizePolicy">
         <sizepolicy hsizetype="Preferred" vsizetype="Fixed">
          <horstretch>0</horstretch>
          <verstretch>0</verstretch>
         </sizepolicy>
        </property>
        <property name="text">
         <string/>
        </property>
        <property name="textFormat">
         <enum>Qt::MarkdownText</enum>
        </property>
        <property name="lightColor" stdset="0">
         <color>
          <red>78</red>
          <green>200</green>
          <blue>12</blue>
         </color>
        </property>
       </widget>
      </item>
      <item row="4" column="0">
       <widget class="SubtitleLabel" name="label_iq_2">
        <property name="sizePolicy">
         <sizepolicy hsizetype="Fixed" vsizetype="Fixed">
          <horstretch>0</horstretch>
          <verstretch>0</verstretch>
         </sizepolicy>
        </property>
        <property name="minimumSize">
         <size>
          <width>80</width>
          <height>0</height>
         </size>
        </property>
        <property name="text">
         <string>Iq:</string>
        </property>
        <property name="textFormat">
         <enum>Qt::MarkdownText</enum>
        </property>
        <property name="lightColor" stdset="0">
         <color>
          <red>213</red>
          <green>110</green>
          <blue>7</blue>
         </color>
        </property>
       </widget>
      </item>
      <item row="4" column="1">
       <widget class="SubtitleLabel" name="label_iq">
        <property name="sizePolicy">
         <sizepolicy hsizetype="Preferred" vsizetype="Fixed">
          <horstretch>0</horstretch>
          <verstretch>0</verstretch>
         </sizepolicy>
        </property>
        <property name="text">
         <string/>
        </property>
        <property name="textFormat">
         <enum>Qt::MarkdownText</enum>
        </property>
        <property name="lightColor" stdset="0">
         <color>
          <red>213</red>
          <green>110</green>
          <blue>7</blue>
         </color>
        </property>
       </widget>
      </item>
      <item row="4" column="2">
       <widget class="SubtitleLabel" name="label_ic_2">
        <property name="sizePolicy">
         <sizepolicy hsizetype="Fixed" vsizetype="Fixed">
          <horstretch>0</horstretch>
          <verstretch>0</verstretch>
         </sizepolicy>
        </property>
        <property name="minimumSize">
         <size>
          <width>50</width>
          <height>0</height>
         </size>
        </property>
        <property name="text">
         <string>Ic:</string>
        </property>
        <property name="textFormat">
         <enum>Qt::MarkdownText</enum>
        </property>
        <property name="lightColor" stdset="0">
         <color>
          <red>255</red>
          <green>5</green>
          <blue>30</blue>
         </color>
        </property>
       </widget>
      </item>
      <item row="4" column="3">
       <widget class="SubtitleLabel" name="label_ic">
        <property name="sizePolicy">
         <sizepolicy hsizetype="Preferred" vsizetype="Fixed">
          <horstretch>0</horstretch>
          <verstretch>0</verstretch>
         </sizepolicy>
        </property>
        <property name="text">
         <string/>
        </property>
        <property name="textFormat">
         <enum>Qt::MarkdownText</enum>
        </property>
        <property name="lightColor" stdset="0">
         <color>
          <red>255</red>
          <green>5</green>
          <blue>30</blue>
         </color>
        </property>
       </widget>
      </item>
     </layout>
    </widget>
   </item>
   <item row="0" column="0" colspan="3">
    <widget class="QWidget" name="widget_2" native="true">
     <property name="sizePolicy">
      <sizepolicy hsizetype="Preferred" vsizetype="Fixed">
       <horstretch>0</horstretch>
       <verstretch>0</verstretch>
      </sizepolicy>
     </property>
     <property name="minimumSize">
      <size>
       <width>0</width>
       <height>80</height>
      </size>
     </property>
     <layout class="QGridLayout" name="gridLayout_4">
      <item row="0" column="2" rowspan="2">
       <widget class="LargeTitleLabel" name="LargeTitleLabel">
        <property name="font">
         <font>
          <family>华文行楷</family>
          <pointsize>44</pointsize>
          <weight>50</weight>
          <bold>false</bold>
         </font>
        </property>
        <property name="text">
         <string>电机控制器</string>
        </property>
       </widget>
      </item>
      <item row="0" column="0">
       <widget class="IconInfoBadge" name="badge_online_status">
        <property name="minimumSize">
         <size>
          <width>22</width>
          <height>22</height>
         </size>
        </property>
        <property name="maximumSize">
         <size>
          <width>22</width>
          <height>22</height>
         </size>
        </property>
        <property name="level" stdset="0">
         <string>Error</string>
        </property>
       </widget>
      </item>
      <item row="1" column="3">
       <spacer name="horizontalSpacer_2">
        <property name="orientation">
         <enum>Qt::Horizontal</enum>
        </property>
        <property name="sizeHint" stdset="0">
         <size>
          <width>40</width>
          <height>20</height>
         </size>
        </property>
       </spacer>
      </item>
      <item row="1" column="1">
       <spacer name="horizontalSpacer">
        <property name="orientation">
         <enum>Qt::Horizontal</enum>
        </property>
        <property name="sizeHint" stdset="0">
         <size>
          <width>40</width>
          <height>20</height>
         </size>
        </property>
       </spacer>
      </item>
      <item row="0" column="1">
       <widget class="SubtitleLabel" name="label_online_status">
        <property name="text">
         <string>以太网离线</string>
        </property>
       </widget>
      </item>
     </layout>
    </widget>
   </item>
   <item row="2" column="0" colspan="3">
    <widget class="CardWidget" name="CardWidget_3">
     <property name="sizePolicy">
      <sizepolicy hsizetype="Preferred" vsizetype="Fixed">
       <horstretch>0</horstretch>
       <verstretch>0</verstretch>
      </sizepolicy>
     </property>
     <layout class="QHBoxLayout" name="horizontalLayout_3">
      <item>
       <widget class="PillPushButton" name="btn_reset_dev">
        <property name="sizePolicy">
         <sizepolicy hsizetype="Fixed" vsizetype="Fixed">
          <horstretch>0</horstretch>
          <verstretch>0</verstretch>
         </sizepolicy>
        </property>
        <property name="minimumSize">
         <size>
          <width>121</width>
          <height>121</height>
         </size>
        </property>
        <property name="font">
         <font>
          <pointsize>22</pointsize>
          <weight>75</weight>
          <bold>true</bold>
         </font>
        </property>
        <property name="text">
         <string>复位</string>
        </property>
        <property name="checkable">
         <bool>false</bool>
        </property>
        <property name="hasIcon" stdset="0">
         <bool>false</bool>
        </property>
       </widget>
      </item>
      <item>
       <widget class="PillPushButton" name="btn_launch_dev">
        <property name="sizePolicy">
         <sizepolicy hsizetype="Fixed" vsizetype="Fixed">
          <horstretch>0</horstretch>
          <verstretch>0</verstretch>
         </sizepolicy>
        </property>
        <property name="minimumSize">
         <size>
          <width>121</width>
          <height>121</height>
         </size>
        </property>
        <property name="font">
         <font>
          <pointsize>22</pointsize>
          <weight>75</weight>
          <bold>true</bold>
         </font>
        </property>
        <property name="text">
         <string>启动</string>
        </property>
        <property name="checkable">
         <bool>true</bool>
        </property>
       </widget>
      </item>
      <item>
       <widget class="PillPushButton" name="btn_stop_dev">
        <property name="sizePolicy">
         <sizepolicy hsizetype="Fixed" vsizetype="Fixed">
          <horstretch>0</horstretch>
          <verstretch>0</verstretch>
         </sizepolicy>
        </property>
        <property name="minimumSize">
         <size>
          <width>121</width>
          <height>121</height>
         </size>
        </property>
        <property name="font">
         <font>
          <pointsize>22</pointsize>
          <weight>75</weight>
          <bold>true</bold>
         </font>
        </property>
        <property name="text">
         <string>停止</string>
        </property>
        <property name="checkable">
         <bool>true</bool>
        </property>
        <property name="checked">
         <bool>false</bool>
        </property>
       </widget>
      </item>
      <item>
       <widget class="PillPushButton" name="btn_calibrate_dev">
        <property name="sizePolicy">
         <sizepolicy hsizetype="Fixed" vsizetype="Fixed">
          <horstretch>0</horstretch>
          <verstretch>0</verstretch>
         </sizepolicy>
        </property>
        <property name="minimumSize">
         <size>
          <width>121</width>
          <height>121</height>
         </size>
        </property>
        <property name="font">
         <font>
          <pointsize>22</pointsize>
          <weight>75</weight>
          <bold>true</bold>
         </font>
        </property>
        <property name="text">
         <string>校准</string>
        </property>
        <property name="checkable">
         <bool>false</bool>
        </property>
       </widget>
      </item>
     </layout>
    </widget>
   </item>
   <item row="1" column="1" colspan="2">
    <widget class="CardWidget" name="CardWidget_2">
     <property name="sizePolicy">
      <sizepolicy hsizetype="Expanding" vsizetype="Expanding">
       <horstretch>0</horstretch>
       <verstretch>0</verstretch>
      </sizepolicy>
     </property>
     <layout class="QGridLayout" name="gridLayout_2">
      <item row="0" column="0" colspan="3">
       <layout class="QHBoxLayout" name="horizontalLayout">
        <property name="sizeConstraint">
         <enum>QLayout::SetFixedSize</enum>
        </property>
        <property name="topMargin">
         <number>0</number>
        </property>
        <property name="bottomMargin">
         <number>0</number>
        </property>
        <item>
         <widget class="SubtitleLabel" name="label_speed">
          <property name="sizePolicy">
           <sizepolicy hsizetype="Preferred" vsizetype="Fixed">
            <horstretch>0</horstretch>
            <verstretch>0</verstretch>
           </sizepolicy>
          </property>
          <property name="minimumSize">
           <size>
            <width>0</width>
            <height>0</height>
           </size>
          </property>
          <property name="text">
           <string>转速：</string>
          </property>
          <property name="textFormat">
           <enum>Qt::MarkdownText</enum>
          </property>
         </widget>
        </item>
        <item>
         <widget class="SubtitleLabel" name="SubtitleLabel_3">
          <property name="sizePolicy">
           <sizepolicy hsizetype="Fixed" vsizetype="Fixed">
            <horstretch>0</horstretch>
            <verstretch>0</verstretch>
           </sizepolicy>
          </property>
          <property name="minimumSize">
           <size>
            <width>0</width>
            <height>0</height>
           </size>
          </property>
          <property name="text">
           <string>rpm</string>
          </property>
         </widget>
        </item>
        <item>
         <widget class="DoubleSpinBox" name="spinbox_speed">
          <property name="sizePolicy">
           <sizepolicy hsizetype="Preferred" vsizetype="Fixed">
            <horstretch>0</horstretch>
            <verstretch>0</verstretch>
           </sizepolicy>
          </property>
          <property name="minimumSize">
           <size>
            <width>0</width>
            <height>44</height>
           </size>
          </property>
          <property name="maximumSize">
           <size>
            <width>16777215</width>
            <height>16777215</height>
           </size>
          </property>
          <property name="font">
           <font>
            <pointsize>16</pointsize>
            <weight>50</weight>
            <bold>false</bold>
            <stylestrategy>PreferAntialias</stylestrategy>
            <kerning>true</kerning>
           </font>
          </property>
          <property name="frame">
           <bool>true</bool>
          </property>
          <property name="alignment">
           <set>Qt::AlignLeading|Qt::AlignLeft|Qt::AlignVCenter</set>
          </property>
          <property name="readOnly">
           <bool>false</bool>
          </property>
          <property name="buttonSymbols">
           <enum>QAbstractSpinBox::NoButtons</enum>
          </property>
          <property name="showGroupSeparator" stdset="0">
           <bool>false</bool>
          </property>
          <property name="prefix">
           <string/>
          </property>
          <property name="minimum">
           <double>-3000.000000000000000</double>
          </property>
          <property name="maximum">
           <double>3000.000000000000000</double>
          </property>
          <property name="singleStep">
           <double>10.000000000000000</double>
          </property>
          <property name="stepType">
           <enum>QAbstractSpinBox::DefaultStepType</enum>
          </property>
         </widget>
        </item>
        <item>
         <widget class="PushButton" name="btn_speed_set">
          <property name="minimumSize">
           <size>
            <width>0</width>
            <height>44</height>
           </size>
          </property>
          <property name="font">
           <font>
            <pointsize>16</pointsize>
            <weight>75</weight>
            <bold>true</bold>
            <stylestrategy>PreferAntialias</stylestrategy>
           </font>
          </property>
          <property name="text">
           <string>设置转速</string>
          </property>
         </widget>
        </item>
        <item>
         <widget class="SwitchButton" name="sw_btn_host_computer">
          <property name="sizePolicy">
           <sizepolicy hsizetype="Fixed" vsizetype="Preferred">
            <horstretch>0</horstretch>
            <verstretch>0</verstretch>
           </sizepolicy>
          </property>
          <property name="font">
           <font>
            <pointsize>9</pointsize>
            <weight>50</weight>
            <bold>false</bold>
           </font>
          </property>
          <property name="checked">
           <bool>true</bool>
          </property>
          <property name="text">
           <string>上位机开启</string>
          </property>
          <property name="onText">
           <string>上位机开启</string>
          </property>
          <property name="offText">
           <string>上位机关闭</string>
          </property>
         </widget>
        </item>
       </layout>
      </item>
      <item row="2" column="0" colspan="3">
       <widget class="HorizontalSeparator" name="HorizontalSeparator"/>
      </item>
      <item row="3" column="0">
       <widget class="QWidget" name="widget_3" native="true">
        <layout class="QGridLayout" name="gridLayout_5">
         <item row="0" column="0">
          <widget class="SubtitleLabel" name="label_2">
           <property name="sizePolicy">
            <sizepolicy hsizetype="Preferred" vsizetype="Fixed">
             <horstretch>0</horstretch>
             <verstretch>0</verstretch>
            </sizepolicy>
           </property>
           <property name="text">
            <string>角度：</string>
           </property>
           <property name="textFormat">
            <enum>Qt::MarkdownText</enum>
           </property>
          </widget>
         </item>
         <item row="1" column="0">
          <widget class="SubtitleLabel" name="label_angle">
           <property name="sizePolicy">
            <sizepolicy hsizetype="Preferred" vsizetype="Fixed">
             <horstretch>0</horstretch>
             <verstretch>0</verstretch>
            </sizepolicy>
           </property>
           <property name="text">
            <string/>
           </property>
           <property name="textFormat">
            <enum>Qt::MarkdownText</enum>
           </property>
          </widget>
         </item>
         <item row="2" column="0">
          <widget class="SubtitleLabel" name="label_1">
           <property name="sizePolicy">
            <sizepolicy hsizetype="Preferred" vsizetype="Fixed">
             <horstretch>0</horstretch>
             <verstretch>0</verstretch>
            </sizepolicy>
           </property>
           <property name="text">
            <string>占空比：</string>
           </property>
           <property name="textFormat">
            <enum>Qt::MarkdownText</enum>
           </property>
          </widget>
         </item>
         <item row="3" column="0">
          <widget class="SubtitleLabel" name="label_duty_cycle">
           <property name="sizePolicy">
            <sizepolicy hsizetype="Preferred" vsizetype="Fixed">
             <horstretch>0</horstretch>
             <verstretch>0</verstretch>
            </sizepolicy>
           </property>
           <property name="text">
            <string/>
           </property>
           <property name="textFormat">
            <enum>Qt::MarkdownText</enum>
           </property>
          </widget>
         </item>
         <item row="4" column="0">
          <widget class="SubtitleLabel" name="label_4">
           <property name="sizePolicy">
            <sizepolicy hsizetype="Preferred" vsizetype="Fixed">
             <horstretch>0</horstretch>
             <verstretch>0</verstretch>
            </sizepolicy>
           </property>
           <property name="text">
            <string>逆变温度：</string>
           </property>
           <property name="textFormat">
            <enum>Qt::MarkdownText</enum>
           </property>
          </widget>
         </item>
         <item row="5" column="0">
          <widget class="SubtitleLabel" name="label_temperature">
           <property name="sizePolicy">
            <sizepolicy hsizetype="Preferred" vsizetype="Fixed">
             <horstretch>0</horstretch>
             <verstretch>0</verstretch>
            </sizepolicy>
           </property>
           <property name="text">
            <string/>
           </property>
           <property name="textFormat">
            <enum>Qt::MarkdownText</enum>
           </property>
          </widget>
         </item>
         <item row="6" column="0">
          <widget class="SubtitleLabel" name="label_3">
           <property name="sizePolicy">
            <sizepolicy hsizetype="Preferred" vsizetype="Fixed">
             <horstretch>0</horstretch>
             <verstretch>0</verstretch>
            </sizepolicy>
           </property>
           <property name="text">
            <string>电机温度：</string>
           </property>
           <property name="textFormat">
            <enum>Qt::MarkdownText</enum>
           </property>
          </widget>
         </item>
         <item row="7" column="0">
          <widget class="SubtitleLabel" name="label_motor_temp">
           <property name="sizePolicy">
            <sizepolicy hsizetype="Preferred" vsizetype="Fixed">
             <horstretch>0</horstretch>
             <verstretch>0</verstretch>
            </sizepolicy>
           </property>
           <property name="text">
            <string/>
           </property>
           <property name="textFormat">
            <enum>Qt::MarkdownText</enum>
           </property>
          </widget>
         </item>
         <item row="8" column="0">
          <widget class="SubtitleLabel" name="label_sys_status_2">
           <property name="sizePolicy">
            <sizepolicy hsizetype="Preferred" vsizetype="Fixed">
             <horstretch>0</horstretch>
             <verstretch>0</verstretch>
            </sizepolicy>
           </property>
           <property name="text">
            <string>系统状态：</string>
           </property>
           <property name="textFormat">
            <enum>Qt::MarkdownText</enum>
           </property>
          </widget>
         </item>
         <item row="9" column="0">
          <widget class="SubtitleLabel" name="label_sys_status">
           <property name="sizePolicy">
            <sizepolicy hsizetype="Preferred" vsizetype="Fixed">
             <horstretch>0</horstretch>
             <verstretch>0</verstretch>
            </sizepolicy>
           </property>
           <property name="text">
            <string/>
           </property>
           <property name="textFormat">
            <enum>Qt::MarkdownText</enum>
           </property>
          </widget>
         </item>
        </layout>
       </widget>
      </item>
      <item row="3" column="1">
       <widget class="VerticalSeparator" name="VerticalSeparator"/>
      </item>
      <item row="3" column="2">
       <widget class="QWidget" name="widget" native="true">
        <property name="sizePolicy">
         <sizepolicy hsizetype="Fixed" vsizetype="Preferred">
          <horstretch>0</horstretch>
          <verstretch>0</verstretch>
         </sizepolicy>
        </property>
        <layout class="QVBoxLayout" name="verticalLayout">
         <item>
          <widget class="SubtitleLabel" name="label_dev_error">
           <property name="sizePolicy">
            <sizepolicy hsizetype="Preferred" vsizetype="Fixed">
             <horstretch>0</horstretch>
             <verstretch>0</verstretch>
            </sizepolicy>
           </property>
           <property name="text">
            <string>报错：</string>
           </property>
           <property name="textFormat">
            <enum>Qt::MarkdownText</enum>
           </property>
          </widget>
         </item>
         <item>
          <widget class="ListWidget" name="list_dev_error">
           <property name="sizePolicy">
            <sizepolicy hsizetype="Preferred" vsizetype="Preferred">
             <horstretch>0</horstretch>
             <verstretch>0</verstretch>
            </sizepolicy>
           </property>
           <property name="font">
            <font>
             <family>0xProto Nerd Font</family>
             <pointsize>12</pointsize>
            </font>
           </property>
           <item>
            <property name="text">
             <string>无</string>
            </property>
           </item>
          </widget>
         </item>
         <item>
          <widget class="SubtitleLabel" name="label_fault_status">
           <property name="sizePolicy">
            <sizepolicy hsizetype="Preferred" vsizetype="Fixed">
             <horstretch>0</horstretch>
             <verstretch>0</verstretch>
            </sizepolicy>
           </property>
           <property name="text">
            <string>故障状态：</string>
           </property>
           <property name="textFormat">
            <enum>Qt::MarkdownText</enum>
           </property>
          </widget>
         </item>
         <item>
          <widget class="ListWidget" name="list_fault_status">
           <property name="sizePolicy">
            <sizepolicy hsizetype="Preferred" vsizetype="Preferred">
             <horstretch>0</horstretch>
             <verstretch>0</verstretch>
            </sizepolicy>
           </property>
           <property name="font">
            <font>
             <family>0xProto Nerd Font</family>
             <pointsize>12</pointsize>
            </font>
           </property>
           <item>
            <property name="text">
             <string>无故障</string>
            </property>
           </item>
          </widget>
         </item>
        </layout>
       </widget>
      </item>
      <item row="1" column="0" colspan="3">
       <layout class="QHBoxLayout" name="horizontalLayout_4">
        <item>
         <widget class="SubtitleLabel" name="label_speed_2">
          <property name="sizePolicy">
           <sizepolicy hsizetype="Preferred" vsizetype="Fixed">
            <horstretch>0</horstretch>
            <verstretch>0</verstretch>
           </sizepolicy>
          </property>
          <property name="minimumSize">
           <size>
            <width>0</width>
            <height>0</height>
           </size>
          </property>
          <property name="text">
           <string>速度设置步伐：</string>
          </property>
          <property name="textFormat">
           <enum>Qt::MarkdownText</enum>
          </property>
         </widget>
        </item>
        <item>
         <widget class="RadioButton" name="radio_btn_speed_1">
          <property name="text">
           <string>x1</string>
          </property>
         </widget>
        </item>
        <item>
         <widget class="RadioButton" name="radio_btn_speed_10">
          <property name="text">
           <string>x10</string>
          </property>
          <property name="checked">
           <bool>true</bool>
          </property>
         </widget>
        </item>
        <item>
         <widget class="RadioButton" name="radio_btn_speed_100">
          <property name="text">
           <string>x100</string>
          </property>
         </widget>
        </item>
       </layout>
      </item>
     </layout>
    </widget>
   </item>
  </layout>
 </widget>
 <customwidgets>
  <customwidget>
   <class>PushButton</class>
   <extends>QPushButton</extends>
   <header>qfluentwidgets</header>
  </customwidget>
  <customwidget>
   <class>PillPushButton</class>
   <extends>ToggleButton</extends>
   <header>qfluentwidgets</header>
  </customwidget>
  <customwidget>
   <class>SwitchButton</class>
   <extends>QWidget</extends>
   <header>qfluentwidgets</header>
  </customwidget>
  <customwidget>
   <class>RadioButton</class>
   <extends>QRadioButton</extends>
   <header>qfluentwidgets</header>
  </customwidget>
  <customwidget>
   <class>ToggleButton</class>
   <extends>PushButton</extends>
   <header>qfluentwidgets</header>
  </customwidget>
  <customwidget>
   <class>HorizontalSeparator</class>
   <extends>QWidget</extends>
   <header>qfluentwidgets</header>
  </customwidget>
  <customwidget>
   <class>VerticalSeparator</class>
   <extends>QWidget</extends>
   <header>qfluentwidgets</header>
  </customwidget>
  <customwidget>
   <class>CardWidget</class>
   <extends>QFrame</extends>
   <header>qfluentwidgets</header>
   <container>1</container>
  </customwidget>
  <customwidget>
   <class>SubtitleLabel</class>
   <extends>QLabel</extends>
   <header>qfluentwidgets</header>
  </customwidget>
  <customwidget>
   <class>LargeTitleLabel</class>
   <extends>QLabel</extends>
   <header>qfluentwidgets</header>
  </customwidget>
  <customwidget>
   <class>InfoBadge</class>
   <extends>QLabel</extends>
   <header>qfluentwidgets</header>
  </customwidget>
  <customwidget>
   <class>IconInfoBadge</class>
   <extends>InfoBadge</extends>
   <header>qfluentwidgets</header>
  </customwidget>
  <customwidget>
   <class>DoubleSpinBox</class>
   <extends>QDoubleSpinBox</extends>
   <header>qfluentwidgets</header>
  </customwidget>
  <customwidget>
   <class>ListWidget</class>
   <extends>QListWidget</extends>
   <header>qfluentwidgets</header>
  </customwidget>
 </customwidgets>
 <resources/>
 <connections/>
</ui>
